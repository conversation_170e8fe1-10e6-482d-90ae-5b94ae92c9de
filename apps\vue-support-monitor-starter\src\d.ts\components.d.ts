///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///

/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAutoComplete: typeof import('ant-design-vue/es')['AutoComplete']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADirectoryTree: typeof import('ant-design-vue/es')['DirectoryTree']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AFormItemRest: typeof import('ant-design-vue/es')['FormItemRest']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APageHeader: typeof import('ant-design-vue/es')['PageHeader']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    ApartmentOutlined: typeof import('@ant-design/icons-vue')['ApartmentOutlined']
    ApiOutlined: typeof import('@ant-design/icons-vue')['ApiOutlined']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    AQrcode: typeof import('ant-design-vue/es')['QRCode']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AreaChartOutlined: typeof import('@ant-design/icons-vue')['AreaChartOutlined']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ArrowRightOutlined: typeof import('@ant-design/icons-vue')['ArrowRightOutlined']
    ASegmented: typeof import('ant-design-vue/es')['Segmented']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOptGroup: typeof import('ant-design-vue/es')['SelectOptGroup']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    AStatisticCountdown: typeof import('ant-design-vue/es')['StatisticCountdown']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATransfer: typeof import('ant-design-vue/es')['Transfer']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATypographyParagraph: typeof import('ant-design-vue/es')['TypographyParagraph']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BarsOutlined: typeof import('@ant-design/icons-vue')['BarsOutlined']
    BlockOutlined: typeof import('@ant-design/icons-vue')['BlockOutlined']
    CheckCircleFilled: typeof import('@ant-design/icons-vue')['CheckCircleFilled']
    CheckCircleOutlined: typeof import('@ant-design/icons-vue')['CheckCircleOutlined']
    CheckOutlined: typeof import('@ant-design/icons-vue')['CheckOutlined']
    CloseOutlined: typeof import('@ant-design/icons-vue')['CloseOutlined']
    CloudDownloadOutlined: typeof import('@ant-design/icons-vue')['CloudDownloadOutlined']
    CloudOutlined: typeof import('@ant-design/icons-vue')['CloudOutlined']
    CloudServerOutlined: typeof import('@ant-design/icons-vue')['CloudServerOutlined']
    ClusterOutlined: typeof import('@ant-design/icons-vue')['ClusterOutlined']
    CodeEditor: typeof import('./../components/codeEditor/index.vue')['default']
    CodeOutlined: typeof import('@ant-design/icons-vue')['CodeOutlined']
    ColumnHeightOutlined: typeof import('@ant-design/icons-vue')['ColumnHeightOutlined']
    CompositionTransfer: typeof import('./../components/compositionTransfer/composition-transfer.vue')['default']
    CompressOutlined: typeof import('@ant-design/icons-vue')['CompressOutlined']
    CopyOutlined: typeof import('@ant-design/icons-vue')['CopyOutlined']
    CustomDrawer: typeof import('./../components/customDrawer/index.vue')['default']
    CustomInput: typeof import('./../components/customInput/index.vue')['default']
    CustomModal: typeof import('./../components/customModal/index.vue')['default']
    CustomSelect: typeof import('./../components/customSelect/index.vue')['default']
    CustomTable: typeof import('./../components/customTable/index.vue')['default']
    DeleteOutlined: typeof import('@ant-design/icons-vue')['DeleteOutlined']
    DesktopOutlined: typeof import('@ant-design/icons-vue')['DesktopOutlined']
    DownloadOutlined: typeof import('@ant-design/icons-vue')['DownloadOutlined']
    DownOutlined: typeof import('@ant-design/icons-vue')['DownOutlined']
    EditOutlined: typeof import('@ant-design/icons-vue')['EditOutlined']
    EllipsisOutlined: typeof import('@ant-design/icons-vue')['EllipsisOutlined']
    ExclamationCircleOutlined: typeof import('@ant-design/icons-vue')['ExclamationCircleOutlined']
    EyeInvisibleOutlined: typeof import('@ant-design/icons-vue')['EyeInvisibleOutlined']
    EyeOutlined: typeof import('@ant-design/icons-vue')['EyeOutlined']
    FileAddOutlined: typeof import('@ant-design/icons-vue')['FileAddOutlined']
    FileOutlined: typeof import('@ant-design/icons-vue')['FileOutlined']
    FileTextOutlined: typeof import('@ant-design/icons-vue')['FileTextOutlined']
    FileZipOutlined: typeof import('@ant-design/icons-vue')['FileZipOutlined']
    FolderAddOutlined: typeof import('@ant-design/icons-vue')['FolderAddOutlined']
    FullscreenOutlined: typeof import('@ant-design/icons-vue')['FullscreenOutlined']
    HighlightOutlined: typeof import('@ant-design/icons-vue')['HighlightOutlined']
    HolderOutlined: typeof import('@ant-design/icons-vue')['HolderOutlined']
    HomeOutlined: typeof import('@ant-design/icons-vue')['HomeOutlined']
    Index2: typeof import('./../components/logView/index2.vue')['default']
    InfoCircleOutlined: typeof import('@ant-design/icons-vue')['InfoCircleOutlined']
    InfoOutlined: typeof import('@ant-design/icons-vue')['InfoOutlined']
    LayoutOutlined: typeof import('@ant-design/icons-vue')['LayoutOutlined']
    LinkOutlined: typeof import('@ant-design/icons-vue')['LinkOutlined']
    LoadingOutlined: typeof import('@ant-design/icons-vue')['LoadingOutlined']
    LockOutlined: typeof import('@ant-design/icons-vue')['LockOutlined']
    LoginOutlined: typeof import('@ant-design/icons-vue')['LoginOutlined']
    LogoutOutlined: typeof import('@ant-design/icons-vue')['LogoutOutlined']
    LogView: typeof import('./../components/logView/index.vue')['default']
    MenuOutlined: typeof import('@ant-design/icons-vue')['MenuOutlined']
    MessageOutlined: typeof import('@ant-design/icons-vue')['MessageOutlined']
    MinusCircleOutlined: typeof import('@ant-design/icons-vue')['MinusCircleOutlined']
    MoreOutlined: typeof import('@ant-design/icons-vue')['MoreOutlined']
    ParameterWidget: typeof import('./../components/parameterWidget/index.vue')['default']
    PlayCircleOutlined: typeof import('@ant-design/icons-vue')['PlayCircleOutlined']
    PlusOutlined: typeof import('@ant-design/icons-vue')['PlusOutlined']
    PlusSquareOutlined: typeof import('@ant-design/icons-vue')['PlusSquareOutlined']
    ProfileOutlined: typeof import('@ant-design/icons-vue')['ProfileOutlined']
    PushpinOutlined: typeof import('@ant-design/icons-vue')['PushpinOutlined']
    QuestionCircleOutlined: typeof import('@ant-design/icons-vue')['QuestionCircleOutlined']
    ReadOutlined: typeof import('@ant-design/icons-vue')['ReadOutlined']
    RedoOutlined: typeof import('@ant-design/icons-vue')['RedoOutlined']
    ReloadOutlined: typeof import('@ant-design/icons-vue')['ReloadOutlined']
    RestOutlined: typeof import('@ant-design/icons-vue')['RestOutlined']
    RetweetOutlined: typeof import('@ant-design/icons-vue')['RetweetOutlined']
    RocketOutlined: typeof import('@ant-design/icons-vue')['RocketOutlined']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectOutlined: typeof import('@ant-design/icons-vue')['SelectOutlined']
    SettingOutlined: typeof import('@ant-design/icons-vue')['SettingOutlined']
    SkinOutlined: typeof import('@ant-design/icons-vue')['SkinOutlined']
    SolutionOutlined: typeof import('@ant-design/icons-vue')['SolutionOutlined']
    SortAscendingOutlined: typeof import('@ant-design/icons-vue')['SortAscendingOutlined']
    SortDescendingOutlined: typeof import('@ant-design/icons-vue')['SortDescendingOutlined']
    StopFilled: typeof import('@ant-design/icons-vue')['StopFilled']
    StopOutlined: typeof import('@ant-design/icons-vue')['StopOutlined']
    SwapOutlined: typeof import('@ant-design/icons-vue')['SwapOutlined']
    SwitcherOutlined: typeof import('@ant-design/icons-vue')['SwitcherOutlined']
    SyncOutlined: typeof import('@ant-design/icons-vue')['SyncOutlined']
    TableOutlined: typeof import('@ant-design/icons-vue')['TableOutlined']
    TagOutlined: typeof import('@ant-design/icons-vue')['TagOutlined']
    TagsOutlined: typeof import('@ant-design/icons-vue')['TagsOutlined']
    Terminal: typeof import('./../components/terminal/index.vue')['default']
    UnorderedListOutlined: typeof import('@ant-design/icons-vue')['UnorderedListOutlined']
    Upgrade: typeof import('./../components/upgrade/index.vue')['default']
    UploadOutlined: typeof import('@ant-design/icons-vue')['UploadOutlined']
    UserOutlined: typeof import('@ant-design/icons-vue')['UserOutlined']
    VerticalLeftOutlined: typeof import('@ant-design/icons-vue')['VerticalLeftOutlined']
    ViewPre: typeof import('./../components/logView/view-pre.vue')['default']
    WarningOutlined: typeof import('@ant-design/icons-vue')['WarningOutlined']
    WarningTwoTone: typeof import('@ant-design/icons-vue')['WarningTwoTone']
  }
}
