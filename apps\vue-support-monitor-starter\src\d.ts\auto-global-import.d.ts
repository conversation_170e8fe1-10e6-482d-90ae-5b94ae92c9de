///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///

/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const $confirm: typeof import('./global/global')['$confirm']
  const $error: typeof import('./global/global')['$error']
  const $info: typeof import('./global/global')['$info']
  const $message: typeof import('./global/global')['$message']
  const $notification: typeof import('./global/global')['$notification']
  const $success: typeof import('./global/global')['$success']
  const $warning: typeof import('./global/global')['$warning']
  const appStore: typeof import('./global/global')['appStore']
  const guideStore: typeof import('./global/global')['guideStore']
  const jpomWindow: typeof import('./global/global')['jpomWindow']
  const route: typeof import('./global/global')['route']
  const router: typeof import('./global/global')['router']
  const userStore: typeof import('./global/global')['userStore']
}
