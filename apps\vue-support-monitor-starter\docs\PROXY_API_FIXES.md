# 代理API接口修复文档

## 修复概述

本次修复主要解决了 `ServerEditDialog.vue` 组件中代理接口导入路径错误的问题，并在路由中添加了代理管理页面。

## 主要修改内容

### 1. 修正API导入路径

**修改前**：
```typescript
import { type MonitorProxy, getProxyPageList } from "@/api/monitor/gen/proxy";
```

**修改后**：
```typescript
import { type ServerProxy, getEnabledProxyList } from "@/api/server/proxy";
```

**修改原因**：
- 原来的路径 `@/api/monitor/gen/proxy` 指向的是通用代理管理接口
- 服务器编辑对话框需要的是服务器专用的代理接口
- 正确的路径应该是 `@/api/server/proxy`，这里包含了服务器代理相关的接口

### 2. 接口类型修正

**MonitorProxy vs ServerProxy 字段对比**：

| MonitorProxy 字段 | ServerProxy 字段 | 说明 |
|------------------|------------------|------|
| `proxyId` | `monitorSysGenServerProxyId` | 代理ID |
| `proxyName` | `monitorSysGenServerProxyName` | 代理名称 |
| `proxyType` | `monitorSysGenServerProxyType` | 代理类型 |
| `proxyHost` | `monitorSysGenServerProxyHost` | 代理主机 |
| `proxyPort` | `monitorSysGenServerProxyPort` | 代理端口 |
| `proxyStatus` | `monitorSysGenServerProxyStatus` | 代理状态 |
| `proxyDesc` | `monitorSysGenServerProxyDescription` | 代理描述 |

### 3. 修改的具体代码部分

#### 3.1 类型定义修正
```typescript
// 修改前
const proxyList = ref<MonitorProxy[]>([]);
const selectedProxy = ref<MonitorProxy | null>(null);

// 修改后
const proxyList = ref<ServerProxy[]>([]);
const selectedProxy = ref<ServerProxy | null>(null);
```

#### 3.2 分组代理列表修正
```typescript
// 修改前
const groups = [
  { type: 'HTTP', label: 'HTTP 代理', proxies: [] as MonitorProxy[] },
  // ...
];

// 修改后
const groups = [
  { type: 'HTTP', label: 'HTTP 代理', proxies: [] as ServerProxy[] },
  // ...
];
```

#### 3.3 字段访问修正
```typescript
// 修改前
proxy.proxyType
proxy.proxyName
proxy.proxyHost

// 修改后
proxy.monitorSysGenServerProxyType
proxy.monitorSysGenServerProxyName
proxy.monitorSysGenServerProxyHost
```

#### 3.4 API调用修正
```typescript
// 修改前
const result = await getProxyPageList({
  page: 1,
  pageSize: 1000,
  proxyStatus: 1
});

// 修改后
const result = await getEnabledProxyList();
```

### 4. 路由配置添加

在 `monitor-system.ts` 路由配置中添加了代理管理页面：

```typescript
{
  path: "/server/proxy",
  name: "serverProxy",
  component: () => import("@/views/server/modules/proxy-management/index.vue"),
  meta: {
    icon: "ri:router-line",
    title: "代理管理",
    showLink: true,
    showParent: true
  }
}
```

## API接口说明

### ServerProxy 接口定义

```typescript
export interface ServerProxy {
  monitorSysGenServerProxyId?: number;           // 代理ID
  monitorSysGenServerProxyName: string;          // 代理名称
  monitorSysGenServerProxyType: string;          // 代理类型
  monitorSysGenServerProxyHost: string;          // 代理主机
  monitorSysGenServerProxyPort: number;          // 代理端口
  monitorSysGenServerProxyUsername?: string;     // 代理用户名
  monitorSysGenServerProxyPassword?: string;     // 代理密码
  monitorSysGenServerProxyStatus: number;        // 代理状态
  monitorSysGenServerProxyDescription?: string;  // 代理描述
  monitorSysGenServerProxyTimeout?: number;      // 超时时间
  monitorSysGenServerProxyAuthRequired?: number; // 是否需要认证
  monitorSysGenServerProxyTags?: string;         // 代理标签
  monitorSysGenServerProxyLastTestTime?: number; // 最后测试时间
  monitorSysGenServerProxyTestResult?: number;   // 测试结果
  monitorSysGenServerProxyTestLatency?: number;  // 测试延迟
  createTime?: string;                           // 创建时间
  updateTime?: string;                           // 更新时间
  createBy?: string;                             // 创建者
  updateBy?: string;                             // 更新者
}
```

### 主要API函数

1. **getEnabledProxyList()**: 获取启用的代理列表
2. **getProxyListByType(proxyType: string)**: 根据代理类型获取代理列表
3. **getProxyPageList(params: ProxyPageRequest)**: 分页查询代理列表

## 修复验证

### 1. 编译检查
- ✅ TypeScript 类型检查通过
- ✅ 无编译错误
- ✅ 所有字段映射正确

### 2. 功能验证
- ✅ 代理列表正确加载
- ✅ 代理选择功能正常
- ✅ 代理信息显示正确
- ✅ 路由导航正常

### 3. 接口对接
- ✅ 后端接口路径正确
- ✅ 请求参数格式正确
- ✅ 响应数据解析正确

## 注意事项

1. **字段命名规范**：ServerProxy 使用 `monitorSysGenServerProxy` 前缀，与后端实体类保持一致
2. **API路径规范**：服务器相关的代理接口统一使用 `/api/server/proxy` 路径
3. **类型安全**：所有字段访问都使用正确的类型定义，避免运行时错误
4. **向后兼容**：修改不影响其他组件的正常使用

## 后续优化建议

1. **统一接口规范**：建议统一所有代理相关接口的命名规范
2. **类型定义优化**：可以考虑创建通用的代理接口基类
3. **错误处理增强**：添加更详细的错误处理和用户提示
4. **性能优化**：考虑添加代理列表缓存机制

## 相关文件

- `src/views/server/modules/server-management/components/ServerEditDialog.vue`
- `src/api/server/proxy.ts`
- `src/router/modules/monitor-system.ts`
- `src/views/server/modules/proxy-management/index.vue`
