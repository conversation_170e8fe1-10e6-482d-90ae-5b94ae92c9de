/* 全局样式，重置样式 */
html,
body {
  // width: 100vw;
  // height: 100vh;
  margin: 0;
  padding: 0;
}

// .ant-drawer-body {
//   padding: 10px;
// }

.ant-table-wrapper {
  overflow-y: auto;
  overflow-x: auto;
}

.ant-table-body {
  overflow-x: auto !important;
}

.search-input-item {
  width: 140px;
  /* margin-right: 10px; */
}

.filter {
  margin-bottom: 10px;
}
/*
.ant-btn {
  margin-right: 10px;
} */

// .ant-btn-sm {
//   font-size: 12px;
// }

pre {
  margin: 0;
}

// .ant-modal-confirm-body .ant-modal-confirm-content {
//   font-size: 16px;
// }

// .ant-modal-confirm-body .ant-modal-confirm-title {
//   font-size: 18px;
//   font-weight: bold;
//}

.hide-scrollbar *::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}

.hide-scrollbar * {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar pre::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}

.hide-scrollbar pre {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@color-border-last: rgba(140, 140, 140, 0.3);
@color-neutral-last: rgba(140, 140, 140, 0);
@scrollbar-size: 4px;

@-moz-document url-prefix() {
  /* Firefox 专属样式 */
  // 兼容火狐
  * {
    scrollbar-width: thin;
    scrollbar-color: @color-border-last @color-neutral-last;
  }
}

// 滚动条样式
*::-webkit-scrollbar {
  width: @scrollbar-size;
  height: @scrollbar-size;
  border-radius: calc(@scrollbar-size / 2);
  background-color: transparent;
}
// 滚动条-活动按钮
*::-webkit-scrollbar-thumb {
  background: @color-border-last;
  border-radius: calc(@scrollbar-size / 2);
  box-shadow: inset 0 0 calc(@scrollbar-size * 1.5) @color-border-last;
}
// 滚动条背景
*::-webkit-scrollbar-track {
  background-color: @color-neutral-last;
  border-radius: calc(@scrollbar-size / 2);
  box-shadow: inset 0 0 calc(@scrollbar-size * 1.5) @color-neutral-last;
}
